# Gemini Martingale EA - UML Class Diagram

```mermaid
classDiagram
    %% Enums
    class TrendDirection {
        <<enumeration>>
        BULLISH
        BEARISH
        NEUTRAL
    }
    
    class SignalType {
        <<enumeration>>
        BUY
        SELL
        NONE
    }
    
    class OrderType {
        <<enumeration>>
        OP_BUY
        OP_SELL
    }
    
    %% Data Structures
    class OrderInfo {
        +int ticket
        +int orderType
        +double lots
        +double openPrice
        +double profit
        +datetime openTime
        +int magicNumber
    }
    
    %% Configuration Management
    class ConfigurationManager {
        +double InitialLot
        +double Multiplier
        +int MaxTrades
        +int ATR_Period_For_PipStep
        +double ATR_Multiplier_PipStep
        +double MaxDrawdownPercent
        +int TrailingStopActivationPips
        +double TrailingStopAtrMultiplier
        +int RSI_Period
        +int MACD_Fast_EMA
        +int MACD_Slow_EMA
        +int MACD_Signal_SMA
        +int BB_Period
        +double BB_Deviation
        +int TrendFilterBufferPips
        +int MagicNumber
        +LoadConfig() bool
        +ValidateConfig() bool
        +GetParameter(string name) double
    }
    
    %% Signal Management
    class SignalManager {
        -ConfigurationManager config
        -int rsiPeriod
        -int macdFast
        -int macdSlow
        -int macdSignal
        +SignalManager(ConfigurationManager cfg)
        +GetBuySignal() bool
        +GetSellSignal() bool
        +CheckRSICondition(SignalType type) bool
        +CheckMACDCondition(SignalType type) bool
        -CalculateRSI() double
        -CalculateMACD() double[]
    }
    
    %% Trend Filter
    class TrendFilter {
        -ConfigurationManager config
        -int bbPeriod
        -double bbDeviation
        -int bufferPips
        +TrendFilter(ConfigurationManager cfg)
        +GetTrendDirection() TrendDirection
        +IsBullishTrend() bool
        +IsBearishTrend() bool
        +CanExecuteSignal(SignalType signal) bool
        -CalculateBollingerBands() double[]
        -GetCurrentPrice() double
    }
    
    %% Order Management
    class OrderManager {
        -ConfigurationManager config
        -int magicNumber
        -OrderInfo[] orders
        +OrderManager(ConfigurationManager cfg)
        +OpenOrder(OrderType type, double lots, double price) int
        +CloseOrder(int ticket) bool
        +CloseAllOrders() bool
        +GetOrdersByMagic() OrderInfo[]
        +GetTotalProfit() double
        +GetTotalLots() double
        +GetOrderCount() int
        +UpdateOrderInfo() void
        -IsValidOrder(int ticket) bool
    }
    
    %% Trade Management
    class TradeManager {
        -ConfigurationManager config
        -OrderManager orderManager
        -MartingaleStrategy martingale
        -RiskManager riskManager
        +TradeManager(ConfigurationManager cfg, OrderManager om, MartingaleStrategy ms, RiskManager rm)
        +ExecuteBuy() bool
        +ExecuteSell() bool
        +CloseAllPositions() bool
        +CanTrade() bool
        +ProcessTradeSignal(SignalType signal) bool
        -CalculateOrderPrice(OrderType type) double
        -ValidateTradeConditions() bool
    }
    
    %% Martingale Strategy
    class MartingaleStrategy {
        -ConfigurationManager config
        -OrderManager orderManager
        -int atrPeriod
        -double atrMultiplier
        -int maxTrades
        +MartingaleStrategy(ConfigurationManager cfg, OrderManager om)
        +CalculateNextLotSize() double
        +CalculatePipStep() double
        +ShouldAddPosition(OrderType type) bool
        +GetNextOrderPrice(OrderType type) double
        +CanAddMorePositions() bool
        -CalculateATR() double
        -GetLastOrderPrice(OrderType type) double
    }
    
    %% Risk Management
    class RiskManager {
        -ConfigurationManager config
        -OrderManager orderManager
        -double maxDrawdownPercent
        -int trailingStopActivationPips
        -double trailingStopAtrMultiplier
        -bool isTrailingStopActive
        -double trailingStopLevel
        +RiskManager(ConfigurationManager cfg, OrderManager om)
        +CheckDrawdown() bool
        +UpdateTrailingStop() void
        +IsTrailingStopActive() bool
        +ShouldCloseAllPositions() bool
        +CalculateCurrentDrawdown() double
        +ActivateTrailingStop() void
        -CalculateTrailingStopDistance() double
        -GetAccountBalance() double
    }
    
    %% Environment Management
    class EnvironmentManager {
        +CheckTradingPermissions() bool
        +CheckConnection() bool
        +HandleServerErrors(int errorCode) bool
        +IsMarketOpen() bool
        +GetServerTime() datetime
        +RetryOperation(int maxRetries) bool
        -LogError(string message) void
    }
    
    %% User Interface
    class UIManager {
        -ConfigurationManager config
        -OrderManager orderManager
        -RiskManager riskManager
        -MartingaleStrategy martingale
        -TrendFilter trendFilter
        +UIManager(ConfigurationManager cfg, OrderManager om, RiskManager rm, MartingaleStrategy ms, TrendFilter tf)
        +UpdateInfoPanel() void
        +DisplayStatus() void
        +ShowPositionInfo() void
        +ShowRiskInfo() void
        +ShowMartingaleInfo() void
        +ShowTrendInfo() void
        -CreateInfoPanel() void
        -UpdatePanelText(string text) void
    }
    
    %% Main EA Controller
    class GeminiMartingaleEA {
        -ConfigurationManager config
        -SignalManager signalManager
        -TrendFilter trendFilter
        -OrderManager orderManager
        -TradeManager tradeManager
        -MartingaleStrategy martingaleStrategy
        -RiskManager riskManager
        -EnvironmentManager environmentManager
        -UIManager uiManager
        -bool isInitialized
        -bool tradingEnabled
        +OnInit() int
        +OnTick() void
        +OnDeinit() void
        +Initialize() bool
        +ProcessTick() void
        +Shutdown() void
        -InitializeComponents() bool
        -CheckTradingConditions() bool
        -ProcessSignals() void
        -UpdateUI() void
    }
    
    %% Relationships
    GeminiMartingaleEA --> ConfigurationManager : uses
    GeminiMartingaleEA --> SignalManager : contains
    GeminiMartingaleEA --> TrendFilter : contains
    GeminiMartingaleEA --> OrderManager : contains
    GeminiMartingaleEA --> TradeManager : contains
    GeminiMartingaleEA --> MartingaleStrategy : contains
    GeminiMartingaleEA --> RiskManager : contains
    GeminiMartingaleEA --> EnvironmentManager : contains
    GeminiMartingaleEA --> UIManager : contains
    
    SignalManager --> ConfigurationManager : uses
    SignalManager --> SignalType : returns
    
    TrendFilter --> ConfigurationManager : uses
    TrendFilter --> TrendDirection : returns
    TrendFilter --> SignalType : validates
    
    OrderManager --> ConfigurationManager : uses
    OrderManager --> OrderInfo : manages
    OrderManager --> OrderType : handles
    
    TradeManager --> ConfigurationManager : uses
    TradeManager --> OrderManager : uses
    TradeManager --> MartingaleStrategy : uses
    TradeManager --> RiskManager : uses
    TradeManager --> SignalType : processes
    
    MartingaleStrategy --> ConfigurationManager : uses
    MartingaleStrategy --> OrderManager : uses
    MartingaleStrategy --> OrderType : calculates for
    
    RiskManager --> ConfigurationManager : uses
    RiskManager --> OrderManager : monitors
    
    UIManager --> ConfigurationManager : displays
    UIManager --> OrderManager : displays
    UIManager --> RiskManager : displays
    UIManager --> MartingaleStrategy : displays
    UIManager --> TrendFilter : displays
```

## 類圖說明

### 主要組件：

1. **GeminiMartingaleEA** - 主控制器，協調所有組件
2. **ConfigurationManager** - 管理所有可配置參數
3. **SignalManager** - 處理 RSI 和 MACD 交易訊號
4. **TrendFilter** - 使用布林通道進行趨勢過濾
5. **OrderManager** - 管理所有交易訂單
6. **TradeManager** - 執行交易操作
7. **MartingaleStrategy** - 實現馬丁格爾策略
8. **RiskManager** - 風險控制和追蹤止損
9. **EnvironmentManager** - 環境檢查和錯誤處理
10. **UIManager** - 使用者介面顯示

### 設計原則：
- 模組化設計，每個類別負責特定功能
- 依賴注入，降低耦合度
- 配置驅動，便於參數調整
- 符合物件導向設計原則
